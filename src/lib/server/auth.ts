import { hash, verify, type Options } from '@node-rs/argon2';

const MY_ARGON2_OPTIONS: Options = {
  memoryCost: 2 ** 14, // 16 MiB of memory
  timeCost: 3, // 3 seconds of computation time
  outputLen: 32,
  parallelism: 1,
};

/**
 * Verifies a password against a hash using Argon2.
 * @param passwordHash The hash to verify against.
 * @param password The password to verify.
 * @returns A boolean indicating whether the password is valid.
 */
export async function verifyHashPassword(passwordHash: string, password: string) {
  const success = await verify(passwordHash, password, MY_ARGON2_OPTIONS);

  return success;
}

/**
 * Hashes a password using Argon2.
 * @param password The password to hash.
 * @returns The hashed password.
 */
export async function hashPassword(password: string) {
  const passwordHash = await hash(password, MY_ARGON2_OPTIONS);

  return passwordHash;
}
