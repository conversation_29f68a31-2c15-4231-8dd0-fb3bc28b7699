import { initTRPC } from '@trpc/server';
import { type FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';
import superjson from 'superjson';
import { headers } from 'next/headers';

import { db } from '@/services/db';
import { auth } from '@/services/better-auth/auth';

export const createTRPCContext = async (opts?: FetchCreateContextFnOptions) => {
  /**
   * @see: https://trpc.io/docs/server/context
   */
  // 1. Get Headers
  // If the function is called from an API route (opts exists), we use opts.req.headers.
  // If the function is called from a Server Component (opts does not exist), we use the headers() function from next/headers.
  // This pattern ensures we can always get the request headers.
  const sourceHeaders = opts?.req.headers ? opts.req.headers : await headers();
  const session = await auth.api.getSession({
    headers: sourceHeaders,
  });

  return {
    db,
    session, // The user's session (or null if not authenticated)
    headers: sourceHeaders,
  };
};
export type Context = Awaited<ReturnType<typeof createTRPCContext>>;

// Avoid exporting the entire t-object
// since it's not very descriptive.
// For instance, the use of a t variable
// is common in i18n libraries.
const t = initTRPC.context<Context>().create({
  /**
   * @see https://trpc.io/docs/server/data-transformers
   */
  transformer: superjson,
});

// Base router and procedure helpers
export const router = t.router;
export const createCallerFactory = t.createCallerFactory;
export const publicProcedure = t.procedure;
