import { drizzle, type PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

import { serverEnv } from '@/env/server';
import * as auth from '@/services/db/schema/auth';

const schema = { ...auth };

declare global {
  // eslint-disable-next-line no-var
  var globalDb: PostgresJsDatabase<typeof schema> | undefined;
}

let db: PostgresJsDatabase<typeof schema>;

if (!serverEnv.DATABASE_URL) {
  throw new Error('DATABASE_URL is not set');
}

if (serverEnv.NODE_ENV === 'production') {
  // In production, we create a new connection for each request
  try {
    // If you decide to use connection pooling via Supabase (described here), and have “Transaction” pool mode enabled, then ensure to turn off prepare, as prepared statements are not supported.
    console.log('Creating new database instance...');
    db = drizzle(postgres(serverEnv.DATABASE_URL, { prepare: false }), { schema });
  } catch (error) {
    console.error('Failed to connect to database:', error);
    process.exit(1);
  }
} else {
  // In development, we use a global variable to avoid recreating the connection between hot reloads
  if (!global.globalDb) {
    console.log('Creating new database instance...');
    global.globalDb = drizzle(postgres(serverEnv.DATABASE_URL, { prepare: false }), { schema });
  }
  db = global.globalDb;
}

export { db };
