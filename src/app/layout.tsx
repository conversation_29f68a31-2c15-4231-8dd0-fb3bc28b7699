import { Inter, JetBrains_Mono } from 'next/font/google';

import { TRPCReactProvider } from '@/trpc/client';
import { Toaster } from '@/components/toaster';

import '@/styles/global.css';

const inter = Inter({
  variable: '--font-sans',
  subsets: ['latin'],
});

const jb = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang='zh-CN' dir='ltr' suppressHydrationWarning>
      <body className={`${inter.variable} ${jb.variable}`}>
        <TRPCReactProvider>{children}</TRPCReactProvider>
        <Toaster />
      </body>
    </html>
  );
}
