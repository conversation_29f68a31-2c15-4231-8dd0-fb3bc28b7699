# shop-center Project Summary

This document provides a comprehensive overview of the `shop-center` project, covering its setup, structure, and conventions.

## 1. Project Overview

`shop-center` is a Next.js application designed with a robust backend powered by Hono and tRPC, utilizing Drizzle ORM for database interactions. It aims to provide a scalable and type-safe development experience.

## 2. Technologies Used

- **Frontend**: Next.js, React, TypeScript
- **Backend**: tRPC, Hono
- **Database**: Drizzle ORM, PostgreSQL
- **UI and Styling**: Shadcn UI, Tailwind CSS
- **Validation**: Zod

## 3. Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js**: Version `^20.9.0` or higher.
- **pnpm**: This project uses `pnpm` for package management.
- **PostgreSQL**: A running PostgreSQL database instance.

### Installation

1.  Clone the repository.
2.  Navigate to the project root.
3.  Install the project dependencies:
    ```bash
    pnpm install
    ```

### Environment Variables

Create a `.env` file in the project root based on `.env.example` (if available, otherwise create one with essential variables). A crucial variable will be your database connection string:

```
DATABASE_URL="postgresql://user:password@host:port/database"
```

### Database Setup

This project uses Drizzle ORM for database schema management.

- **Push schema to database**:
  ```bash
  pnpm run db:push
  ```
- **Generate Drizzle migrations**:
  ```bash
  pnpm run db:generate
  ```
- **Apply database migrations**:
  ```bash
  pnpm run db:migrate
  ```

### Running the Development Server

To start the Next.js development server with Turbopack:

```bash
pnpm run dev
```

The application will typically be accessible at `http://localhost:3000`.

### Building for Production

To build the project for production:

```bash
pnpm run build
```

To start the production server:

```bash
pnpm run start
```

## 4. Project Structure (`src/` directory)

```mermaid
graph TD
    A[src/] --> B[app/]
    A --> C[components/]
    A --> D[hono/]
    A --> E[lib/]
    A --> F[services/]
    A --> G[styles/]
    A --> H[trpc/]

    B --> B1[app/api/]
    B --> B2[app/client-hello.tsx]
    B --> B3[app/layout.tsx]
    B --> B4[app/page.tsx]

    B1 --> B1_1[app/api/[...endpoint]/route.ts]
    B1 --> B1_2[app/api/trpc/[trpc]/route.ts]

    C --> C1[components/ui/]

    D --> D1[hono/client.ts]
    D --> D2[hono/index.ts]
    D --> D3[hono/routes/]

    F --> F1[services/db/]

    F1 --> F1_1[services/db/index.ts]
    F1 --> F1_2[services/db/schema/]

    H --> H1[trpc/client.tsx]
    H --> H2[trpc/index.ts]
    H --> H3[trpc/query-client.ts]
    H --> H4[trpc/server.ts]
    H --> H5[trpc/routers/]
```

- **`src/app/`**: Contains Next.js App Router pages and API routes.
  - `src/app/api/`: Houses Next.js API routes, including a catch-all route for Hono integration and the dedicated tRPC endpoint.
- **`src/components/`**: Stores reusable React components. The `ui/` subdirectory likely contains UI components from Shadcn UI.
- **`src/hono/`**: Dedicated to Hono API server setup and route definitions, facilitating a separate API layer.
- **`src/lib/`**: A collection of general utility functions and shared helper modules used across the application.
- **`src/services/db/`**: Manages database connection, configuration, and Drizzle ORM schema definitions, ensuring clear separation of database logic.
- **`src/styles/`**: Holds global CSS styles, primarily configured for Tailwind CSS.
- **`src/trpc/`**: Contains the tRPC client and server setup, including router definitions and integration with React Query for efficient data fetching.

## 5. Project Conventions

### Code Formatting

This project enforces consistent code formatting using Prettier.

- To format all relevant files:
  ```bash
  pnpm run format
  ```

### Linting

ESLint is used to maintain code quality and identify potential issues.

- To run the linter and fix fixable issues:
  ```bash
  pnpm run lint
  ```

### Commit Messages

Conventional Commits are enforced using Commitizen and Commitlint to ensure clear and consistent commit history.

- To create a guided commit message:
  ```bash
  pnpm run cmt
  ```

## 6. Next Steps

- Explore the `src/app/api/trpc` and `src/hono` directories to understand the API layer.
- Review `src/services/db/schema` for database model definitions.
- Contribute by adding new features or improving existing ones following the established conventions.
