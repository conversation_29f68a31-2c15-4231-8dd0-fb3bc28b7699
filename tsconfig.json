{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "noEmit": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "noImplicitThis": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "target": "esnext", "verbatimModuleSyntax": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@@/*": ["./*"]}}, "include": ["next-env.d.ts", "src/types.d.ts", "**/*.ts", "**/*.tsx", "**/*.mdx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}