{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"], "disabled": false, "alwaysAllow": ["get-library-docs", "resolve-library-id"]}, "shadcn-ui-server": {"command": "npx", "args": ["@heilgar/shadcn-ui-mcp-server"], "alwaysAllow": ["list-components"]}}}