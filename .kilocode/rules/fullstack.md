**Technologies:** Next.js (App Router), React

- This project uses the Next.js App Router exclusively. No `pages` directory.
- New pages must be created as `page.tsx` inside a new directory in `src/app`.
- Use `layout.tsx` for UI that is shared across multiple pages.
- Components are Server Components (RSCs) by default. Keep them this way unless interactivity is required.
- Use the `'use client';` directive only when hooks (`useState`, `useEffect`) or event listeners are necessary.
- Push client components to the leaves of the component tree (make them as small as possible).
- Pass Server Components as `children` props to Client Components to avoid making large components client-side.
- In RSCs, fetch data directly using server-side code (e.g., calling a db function).
- Use a `loading.tsx` file to create meaningful loading UI with React Suspense.
- Use an `error.tsx` file to handle runtime errors within a route segment.
- Use the Next.js `<Link>` component for all internal navigation to enable pre-fetching.
- Use the `generateMetadata` function in `page.tsx` or `layout.tsx` for SEO and page titles.
- For simple form mutations from the server, prefer using Server Actions.
- Use Route Handlers (`route.ts`) only for creating webhook endpoints or non-tRPC APIs.
- Use the `useRouter` hook from `next/navigation`, not `next/router`.
- Environment variables must be prefixed with `NEXT_PUBLIC_` to be available on the client.
- Store fonts in `src/app/fonts` and load them using `@next/font`.
- Use the Next.js `<Image>` component for all static or remote images for automatic optimization.
- Dynamic routes are created with bracketed folder names, e.g., `app/users/[id]/page.tsx`.
- Group related routes using parentheses in folder names, e.g., `app/(marketing)/about/page.tsx`.
