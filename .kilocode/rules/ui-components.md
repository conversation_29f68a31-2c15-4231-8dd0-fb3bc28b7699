**Technologies:** Shadcn UI, Radix <PERSON>, `class-variance-authority` (CVA)

- To add a new component, you **must** run `pnpm dlx shadcn-ui@latest add [component-name]`.
- Never install a Radix UI package (e.g., `@radix-ui/react-dialog`) directly.
- All added UI components are stored in `src/components/ui`.
- Do not modify the base files in `src/components/ui` after they are added.
- To customize a component, create a new component that wraps the Shadcn component.
- Use the `asChild` prop to compose components, for example, placing a Next.js `<Link>` inside a `<Button>`.
- Build all forms using the Shadcn `Form` component, which integrates `react-hook-form` and Zod.
- Understand that Shadcn components are primitives; their style comes from Tailwind CSS.
- For compound components (e.g., `Dialog`, `Card`), import all necessary sub-components.
- The `cn` utility is used internally and should be used for customizations.
- Refer to the official Shadcn UI website for component APIs and examples.
- Do not treat Shadcn as a typical versioned npm library; it is a collection of source code snippets.
- When updating a component, you may need to run the `add` command again.
- Use component variants (e.g., `<Button variant="destructive">`) as defined.
- Do not add component logic to the base files in `src/components/ui`.
