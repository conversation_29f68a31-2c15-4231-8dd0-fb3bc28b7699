**Technologies:** tRPC Server Caller, Next.js Server Components, Server Actions

- Use the `serverCaller` for all data fetching inside Next.js Server Components.
- When invoking the caller, you must manually construct and provide the server context.
- For protected procedures, fetch the user's session on the server and pass it into the context you create.
- Call procedures directly as if they were asynchronous functions: `await serverCaller.router.procedure(input)`.
- Pass data fetched with `serverCaller` directly as props from Server Components to Client Components.
- Use `serverCaller` within Server Actions to execute mutations, reusing your tRPC business logic.
- Always wrap `serverCaller` calls in `try...catch` blocks for robust error handling on the server.
- The `serverCaller` is for backend use only; **never** import it into a file marked with `'use client'`.
- Prefer `serverCaller` over `fetch` for calling your own API on the server to eliminate HTTP overhead.
