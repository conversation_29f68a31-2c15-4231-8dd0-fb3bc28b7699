**Technologies:** tRP<PERSON>, <PERSON>od

- Define all tRPC API routers in separate files under `src/trpc/routers/`.
- Use `router()` to initialize a new router file.
- Use `publicProcedure` for public access and `protectedProcedure` for authenticated access.
- Every procedure input **must** be validated with a Zod schema via `.input()`.
- Use descriptive Zod validators like `.min()`, `.email()`, and `.cuid()`.
- Use `.query()` for read-only operations.
- Use `.mutation()` for any data-modifying operations (create, update, delete).
- Access the database client via `ctx.db` inside procedures.
- Access user session information via `ctx.session` in `protectedProcedure`.
- Handle errors by throwing a `TRPCError` with an appropriate code.
- Merge all sub-routers into the main `appRouter` in `src/server/api/root.ts`.
- Never bypass tRPC procedures to access the database from the client.
- Keep business logic within tRPC procedures, not in the client.
- Reuse Zod input schemas for client-side form validation.
- The client should infer all types from the tRPC `appRouter`. Do not create manual client-side types.
- Name procedures clearly and verbosely (e.g., `getUserById`, `createPost`).
- Do not nest complex logic; call other internal functions or services if needed.
- Use Zod's `.transform()` for data transformation, if necessary.
- Zod is the single source of truth for all API input shapes.
- A `protectedProcedure` automatically handles session validation.
