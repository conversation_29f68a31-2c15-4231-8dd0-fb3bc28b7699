# Code Formatting and Linting Rules

This document outlines the code formatting, linting, and TypeScript configuration rules enforced in the `shop-center` project. Adhering to these guidelines ensures code consistency, readability, and helps prevent common errors.

## 1. Code Formatting (Prettier)

Prettier is used for consistent code formatting. The configuration is defined in `.prettierrc.js`.

**To format your code, run:**

```bash
pnpm run format
```

## 2. Code Linting (ESLint)

ESLint is used to identify and report on patterns found in JavaScript/TypeScript code, with the goal of making code more consistent and avoiding bugs. The configuration is defined in `.eslintrc.js`.

**To lint your code and fix fixable issues, run:**

```bash
pnpm run lint
```

## 3. TypeScript Configuration (`tsconfig.json`)

The `tsconfig.json` file defines the root files and the compiler options required to compile the project.

## 4. Tables

When printing tables, always add an exclamation mark to each column header.
