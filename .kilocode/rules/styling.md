**Technologies:** Tai<PERSON><PERSON> CSS, PostCSS

- All styling must be done with Tailwind's utility classes.
- Do not write inline styles (`style={...}`).
- Do not write custom CSS files (`.css`, `.scss`) for component-specific styles.
- Add all custom colors, fonts, or spacing in the `theme.extend` object.
- Use the `cn()` utility function from `src/lib/utils.ts` for all class name merging and conditional logic.
- Use responsive prefixes (`sm:`, `md:`, `lg:`) for creating adaptive layouts.
- Use state prefixes (`hover:`, `focus:`, `disabled:`) for interactive styling.
- Use the `dark:` prefix for all dark mode styles.
- Keep class strings organized and readable, following a consistent order if possible.
- Use `@apply` in global CSS files (`src/styles/globals.css`) sparingly, only for base styles.
- Never use `@apply` inside a component file.
- Before adding a new Tailwind plugin, get project approval.
- Use arbitrary values (e.g., `top-[13px]`) only when a theme value is not suitable.
- Use `group` and `peer` variants for styling based on parent or sibling state.
- All icons should be styled with `currentColor` and sized with `h-` and `w-` utilities.
- Use CSS variables defined in `globals.css` for themeable properties.
- To animate, use Tailwind's built-in animation utilities (`animate-spin`, etc.).
- Do not use non-Tailwind class names.
- PurgeCSS is configured automatically; trust the process.
