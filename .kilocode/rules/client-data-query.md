**Technologies:** TanStack Query, `@trpc/react-query`

- Our tRPC hooks are built on TanStack Query. Leverage its caching capabilities.
- Fetch data using `useQuery(trpc.[routerName].[procedureName].queryOptions())`.
- Modify data using `useMutation(trpc.[routerName].[procedureName].mutationOptions())`.
- After every successful mutation, you **must** invalidate the relevant queries.
- Invalidate queries inside the `onSuccess` callback of `useMutation`.
- Syntax for invalidation: `onSuccess: () => { utils.[routerName].invalidate(); }`.
- Be specific when invalidating to avoid refetching unrelated data.
- Implement optimistic updates for a faster perceived user experience.
- In `onMutate`, cancel existing queries and set optimistic data with `utils.setQueryData`.
- In `onError`, roll back the optimistic update using context data.
- In `onSettled`, always invalidate the query to ensure eventual consistency.
- The `staleTime` option determines how long data is considered fresh. Use it to prevent refetches.
- Use the `enabled` option for queries that depend on other data or user input.
- The `select` option can be used to transform or select a part of the query data.
- For paginated lists, use `useInfiniteQuery`.
- tRPC automatically manages query keys; do not override them.
- Pass server-fetched data as `initialData` to avoid an initial client-side fetch.
- Use `queryClient.getQueryData` to read data from the cache without fetching.
- Use `queryClient.setQueryData` to imperatively update data in the cache.
