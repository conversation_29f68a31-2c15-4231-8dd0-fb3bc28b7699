**Technologies:** <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ORM, Dr<PERSON>zle Kit

- All database table schemas must be defined in `src/services/db/schema/`.
- Use plural, snake_case for table names (e.g., `user_profiles`).
- Use camelCase for column names within the code (e.g., `userId`).
- Define tables using the `pgTable` function from Dr<PERSON>zle.
- Use `serial('id').primaryKey()` for all primary key columns.
- Define all table relationships using the `relations()` helper function.
- Set non-nullable columns with `.notNull()`.
- Specify default values using `.default()`.
- Run `pnpm drizzle-kit generate:pg` after any change to `src/services/db/schema/`.
- After generating, you must run `pnpm db:push` to apply the migration.
- This two-step migration process is mandatory for all schema changes.
- Never write raw `CREATE TABLE` or `ALTER TABLE` statements in the application.
- All database queries must use the Drizzle query client (`ctx.db`).
- Use Drizzle's expression operators (`eq`, `inArray`, `and`, `lt`, `gt`).
- Do not write raw SQL `SELECT` queries in application code.
- For database seeding, use the `pnpm db:seed` script.
- Infer Zod schemas from Drizzle schemas using `drizzle-zod` for validation.
- Keep schema definitions as the single source of truth for data shapes.
- Foreign key constraints must be explicitly defined.
- Use `timestamp()` for date and time columns, specifying timezone if needed.
