# Project Architecture

## 1. Technologies Used

- **Node.js**: Version `^20.9.0` or higher.
- **pnpm**: This project uses `pnpm` for package management.
- **PostgreSQL**: A running PostgreSQL database instance.
- **Frontend**: Next.js, React, TypeScript
- **Backend**: tRPC, Hono
- **Database**: Drizzle ORM, PostgreSQL
- **UI and Styling**: Shadcn UI, Tailwind CSS
- **Validation**: Zod v4 (you should use `import * as z from "zod/v4"`)
